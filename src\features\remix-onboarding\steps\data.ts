import { RoleGroup } from "./type";

export const roleGroups: RoleGroup[] = [
  {
    id: 'songwriter',
    title: 'Songwriter/Composer',
    roles: [
      { key: 'toplineWriter', label: 'Topline Writer' },
      { key: 'lyricist', label: 'Lyricist' },
      { key: 'melodyWriter', label: 'Melody Writer' },
      { key: 'beatmakerTrackmaker', label: 'Beatmaker/Trackmaker' },
      { key: 'arranger', label: 'Arranger' },
      { key: 'remixer', label: 'Remixer' },
      { key: 'orchestrator', label: 'Orchestrator' },
      { key: 'filmTvComposer', label: 'Film/TV Composer' },
      { key: 'jingleWriter', label: 'Jingle Writer' },
    ],
  },
  {
    id: 'producer',
    title: 'Producer',
    roles: [
      { key: 'vocalProducer', label: 'Vocal Producer' },
      { key: 'mixingProducer', label: 'Mixing Producer' },
      { key: 'recordingProducer', label: 'Recording Producer' },
      { key: 'arrangementProducer', label: 'Arrangement Producer' },
      { key: 'synthProducer', label: 'Programmer/Synth Producer' },
      { key: 'soundDesigner', label: 'Sound Designer' },
      { key: 'orchestralProducer', label: 'Orchestral Producer' },
      { key: 'executiveProducer', label: 'Executive Producer' },
      { key: 'beatmaker', label: 'Beatmaker' },
      { key: 'remixerProducer', label: 'Remixer' },
    ],
  },
  {
    id: 'musician',
    title: 'Musician/Instrumentalist',
    roles: [
      { key: 'guitarist', label: 'Guitarist' },
      { key: 'bassPlayer', label: 'Bass Player' }, 
      { key: 'drummer', label: 'Drummer/Percussionist' },
      { key: 'keyboardist', label: 'Keyboardist/Pianist' },
      { key: 'stringsPlayer', label: 'Strings Player' },
      { key: 'woodwindPlayer', label: 'Woodwind Player' },
      { key: 'brassPlayer', label: 'Brass Player' },
      { key: 'folkInstrumentalist', label: 'Folk/Traditional Instrumentalist' },
      { key: 'electronicInstrumentalist', label: 'Electronic Instruments Specialist' },
      { key: 'worldInstrumentalist', label: 'World Music Instrumentalist' },
      { key: 'sessionMusician', label: 'Session Musician' },
      { key: 'multiInstrumentalist', label: 'Multi-Instrumentalist' },
      { key: 'orchestraMember', label: 'Orchestra/Ensemble Member' },
    ],
  },
  {
    id: 'vocalist',
    title: 'Vocalist',
    roles: [
      { key: 'leadVocalist', label: 'Lead Vocalist' },
      { key: 'backgroundVocalist', label: 'Background Vocalist' },
      { key: 'sessionVocalist', label: 'Session Vocalist' },
      { key: 'choirSinger', label: 'Choir/Ensemble Singer' },
      { key: 'operaSinger', label: 'Opera/Classical Singer' },
      { key: 'vocalArranger', label: 'Vocal Arranger' },
      { key: 'beatboxer', label: 'Beatboxer' },
      { key: 'rapVocalist', label: 'Rap Vocalist/MC' },
      { key: 'voiceoverArtist', label: 'Voiceover Artist' },
      { key: 'vocalCoach', label: 'Vocal Coach' },
    ],
  },
  {
    id: 'engineer',
    title: 'Engineer/Editor',
    roles: [
      { key: 'recordingEngineer', label: 'Recording Engineer' },
      { key: 'mixingEngineer', label: 'Mixing Engineer' },
      { key: 'masteringEngineer', label: 'Mastering Engineer' },
      { key: 'audioEditor', label: 'Audio Editor' },
      { key: 'soundDesignerEngineer', label: 'Sound Designer' },
      { key: 'vocalEditor', label: 'Vocal Editor/Tuner' },
      { key: 'liveSoundEngineer', label: 'Live Sound Engineer' },
      { key: 'studioTechnician', label: 'Studio Technician' },
      { key: 'audioRestoration', label: 'Audio Restoration Specialist' },
      { key: 'broadcastEngineer', label: 'Broadcast Audio Engineer' },
    ],
  },
  {
    id: 'artist',
    title: 'Artist/Performer',
    roles: [
      { key: 'soloArtist', label: 'Solo Recording Artist' },
      { key: 'bandMember', label: 'Band/Group Member' },
      { key: 'djArtist', label: 'DJ/Electronic Artist' },
      { key: 'livePerformer', label: 'Live Performer' },
      { key: 'coverArtist', label: 'Cover Artist' },
      { key: 'tributeArtist', label: 'Tribute Artist' },
      { key: 'musicalTheater', label: 'Musical Theater Performer' },
      { key: 'classicalPerformer', label: 'Classical Performer' },
      { key: 'performanceArtist', label: 'Performance Artist' },
    ],
  },
  {
    id: 'other',
    title: 'Other Music Industry',
    roles: [
    { key: 'artistManager', label: 'Artist Manager' },
    { key: 'creativeDirector', label: 'Creative Director / Video Director / Show Director' },
    { key: 'visualContentCreator', label: 'Visual Content Creator / Photographer / Videographer' },
    { key: 'tourProduction', label: 'Tour Production Professional' },
    { key: 'bookingAgent', label: 'Booking Agent' },
    { key: 'musicPromoter', label: 'Promoter' }, // renamed
    { key: 'aAndR', label: 'A&R' }, // renamed
    { key: 'recordLabel', label: 'Record Label' }, // added earlier
    { key: 'musicPublisher', label: 'Publisher' }, // renamed
    { key: 'musicAttorney', label: 'Attorney' }, // renamed
    { key: 'tourManager', label: 'Tour Manager' },
    { key: 'musicMarketer', label: 'Marketing Specialist' }, // renamed
    { key: 'socialMediaManager', label: 'Social Media Manager' }, // ✅ NEW
    { key: 'graphicDesigner', label: 'Graphic Designer' }, // renamed
    { key: 'videoDirector', label: 'Video Director' }, // renamed
    { key: 'musicJournalist', label: 'Journalist' }, // renamed
    { key: 'radioDJ', label: 'DJ/Programmer' }, // renamed
    { key: 'musicSupervisor', label: 'Music Supervisor' },
    { key: 'musicEducator', label: 'Music Educator' },
    { key: 'publicist', label: 'Publicist' }, // added earlier
      { key: 'other', label: 'Other' },
    ],
  }
//   {
//     id: 'none',
//     title: '',
//     roles: [
//       { key: 'notInMusic', label: 'I do not work in music' },
//     ],
//   },
];
