import { useEffect, useState } from 'react';
import { useQuery } from '@apollo/client';
import { GET_PROFILE_PICTURE_URL } from '@/graphql/remix-queries';

export function useProfileImageUrl(profileImageKey: string | null) {
  const [viewUrl, setViewUrl] = useState<string | null>(null);

  // Use useQuery instead of useLazyQuery and skip when no profileImageKey
  const { data, loading, error, refetch } = useQuery(GET_PROFILE_PICTURE_URL, {
    skip: !profileImageKey, // Skip the query if no profileImageKey
    fetchPolicy: 'cache-and-network', // Ensure we get fresh data when cache is cleared
    errorPolicy: 'all'
  });

  // Refetch when profileImageKey changes and is not null
  useEffect(() => {
    if (profileImageKey) {
      refetch();
    } else {
      setViewUrl(null);
    }
  }, [profileImageKey, refetch]);

  useEffect(() => {
    if (data?.getProfilePictureUrl?.viewUrl) {
      setViewUrl(data.getProfilePictureUrl.viewUrl);
    } else if (data && !data.getProfilePictureUrl?.viewUrl) {
      setViewUrl(null);
    }
  }, [data]);

  return { viewUrl, loading, error };
}
